﻿using Pondres.Omnia.Print.Contracts.Api.Enums;
using Pondres.Omnia.Print.Contracts.Common;
using System;

namespace Pondres.Omnia.Print.Contracts.Api.Bundle
{
    public class PrintBundleDefaultFilter
    {
        public string BundleId { get; set; } = string.Empty;
        public string Carrier { get; set; } = string.Empty;
        public string? ContinuationToken { get; set; }
        public string Customer { get; set; } = string.Empty;
        public string BatchName { get; set; } = string.Empty;
        public string DGCode { get; set; } = string.Empty;
        public string GordNumber { get; set; } = string.Empty;
        public DateTimeOffset? MailDateFrom { get; set; }
        public DateTimeOffset? MailDateTo { get; set; }
        public int MaxPageSize { get; set; }
        public DateTimeOffset? CreatedOnFrom { get; set; }
        public DateTimeOffset? CreatedOnTo { get; set; }
        public PrinterType? PrinterType { get; set; }
        public string PrintMode { get; set; } = string.Empty;
        public bool TestCustomers { get; set; }
        public bool IncludeEmptyBundles { get; set; }
        public PrintBundleStateType? State { get; set; }
        public bool? IsDirectlyCompleted { get; set; }
    }
}